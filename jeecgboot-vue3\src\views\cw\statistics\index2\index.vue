<template>
  <!-- 成本统计页面 -->
  <div class="cw-cost-page">
    <!-- 成本概览卡片 -->
    <a-card :bordered="false" title="成本概览">
      <!-- 过滤条件 -->
      <a-space style="margin-bottom: 12px;" align="center">
        <a-radio-group v-model:value="dimension">
          <a-radio-button value="month">月</a-radio-button>
          <a-radio-button value="year">年</a-radio-button>
        </a-radio-group>
        <a-date-picker
          v-model:value="costDate"
          :picker="pickerType"
          :allowClear="false"
        />
      </a-space>

      <!-- 概览数值 -->
      <div class="summary-container">
        <div class="summary-item">
          <div class="label">总成本</div>
          <div class="value">{{ formatNumber(costStat.totalCost) }} <span class="unit">万元</span></div>
        </div>
        <div class="summary-item">
          <div class="label">吨矿成本</div>
          <div class="value">{{ formatNumber(costStat.tonCost) }} <span class="unit">元/吨</span></div>
        </div>
        <div class="summary-item">
          <div class="label">铜金属成本</div>
          <div class="value">{{ formatNumber(costStat.metalCost) }} <span class="unit">元/吨</span></div>
        </div>
        <div class="summary-item">
          <div class="label">人均收入/人月</div>
          <div class="value">{{ formatNumber(costStat.avgOutput) }} <span class="unit">万元/人</span></div>
        </div>
        <div class="summary-item">
          <div class="label">人均利润/人月</div>
          <div class="value">{{ formatNumber(costStat.avgProfit) }} <span class="unit">万元/人</span></div>
        </div>
      </div>
      <div class="period">{{ costStat.period }}</div>
    </a-card>

    <!-- 成本趋势 -->
    <a-card :bordered="false" style="margin-top: 16px;">
      <template #title>
        月度成本趋势
        <!-- 单位选择器 -->
        <a-select v-model:value="selectedUnit" size="small" style="width: 100px; margin-left: 12px;">
          <a-select-option value="all">全部</a-select-option>
          <a-select-option value="ckc" :disabled="isUnitDisabled">采矿场</a-select-option>
          <a-select-option value="ds" :disabled="isUnitDisabled">大山</a-select-option>
          <a-select-option value="jw" :disabled="isUnitDisabled">精尾</a-select-option>
          <a-select-option value="sx" :disabled="isUnitDisabled">泗选</a-select-option>
        </a-select>
        <!-- 折线选择器 -->
        <a-select v-model:value="trendType" size="small" style="width: 120px; margin-left: 12px;">
          <a-select-option value="total">总成本</a-select-option>
          <a-select-option value="ton">吨矿成本</a-select-option>
          <a-select-option value="metal" :disabled="isMetalDisabled">金属成本</a-select-option>
        </a-select>
      </template>
      <a-spin :spinning="costDataLoading" tip="正在加载成本数据...">
        <div ref="trendRef" style="height: 360px;"></div>
      </a-spin>
    </a-card>

    <!-- 单位成本对比 -->
    <a-card title="单位对比" :bordered="false" style="margin-top: 16px;">
      <template #extra>
        <a-switch v-model:checked="barMode" checked-children="双柱" un-checked-children="差异" @change="updateBarChart" />
      </template>
      <!-- 过滤条件 -->
      <a-space style="margin-bottom: 12px;" align="center">
        <a-radio-group v-model:value="barDimension">
          <a-radio-button value="month">月</a-radio-button>
          <a-radio-button value="year">年</a-radio-button>
        </a-radio-group>
        <a-date-picker
          v-model:value="barDate"
          :picker="barPickerType"
          :allowClear="false"
        />
      </a-space>

      <!-- 总成本对比 -->
      <a-divider orientation="left" style="margin-top: 0;">总成本</a-divider>
      <a-spin :spinning="barDataLoading" tip="正在加载单位成本数据...">
        <div ref="barTotalRef" style="height: 360px;"></div>
      </a-spin>

      <!-- 吨矿成本对比 -->
      <a-divider orientation="left">吨矿成本</a-divider>
      <a-spin :spinning="barDataLoading" tip="正在加载单位成本数据...">
        <div ref="barTonRef" style="height: 360px;"></div>
      </a-spin>
    </a-card>
  </div>
</template>

<script lang="ts" setup name="cw-cost-statistics">
  // 页面脚本 - 成本统计
  import { ref, onMounted, watch, computed } from 'vue';
  import dayjs from 'dayjs';
  import { message } from 'ant-design-vue';

  // 接口
  import { costStatistics, costTrendByUnit, costExpandBar } from '/@/api/cw/statistics';
  // 工具
  import { formatNumber } from '/@/utils/showUtils';
  import { useECharts } from '/@/hooks/web/useECharts';

  /** 概览、趋势相关 **/
  const costStat = ref<any>({});
  const costTrendList = ref<any[]>([]);
  const costDate = ref(dayjs());
  const dimension = ref<'month' | 'year'>('month');
  const pickerType = computed(() => dimension.value);
  const costDataLoading = ref(false);

  const trendRef = ref<HTMLDivElement | null>(null);
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore 兼容 null 引用
  const { setOptions: setTrendOptions } = useECharts(trendRef as any);

  /** 单位成本对比相关 **/
  const barDimension = ref<'month' | 'year'>('month');
  const barDate = ref(dayjs());
  const barPickerType = computed(() => barDimension.value);
  const barList = ref<any[]>([]);
  const barDataLoading = ref(false);
  const barTotalRef = ref<HTMLDivElement | null>(null);
  const barTonRef = ref<HTMLDivElement | null>(null);
  // @ts-ignore
  const { setOptions: setBarTotalOptions } = useECharts(barTotalRef as any);
  // @ts-ignore
  const { setOptions: setBarTonOptions } = useECharts(barTonRef as any);

  /** 日计划进度（基于所选时间段与当前日期） */
  const progressRatio = computed(() => {
    const sel = barDate.value;
    if (!sel) return 0;
    const now = dayjs();
    if (barDimension.value === 'month') {
      const selYear = sel.year();
      const selMonth = sel.month();
      const nowYear = now.year();
      const nowMonth = now.month();
      // 选中月份在当前月份之前视为已完成，之后视为未开始
      if (selYear < nowYear || (selYear === nowYear && selMonth < nowMonth)) {
        return 1;
      } else if (selYear > nowYear || (selYear === nowYear && selMonth > nowMonth)) {
        return 0;
      } else {
        // 当前月份，进度 = 今日 / 当月天数
        const daysPassed = now.date();
        const daysInMonth = now.daysInMonth();
        return Math.min(1, Math.max(0, daysPassed / daysInMonth));
      }
    }
    // 年度维度
    const selYear = sel.year();
    const nowYear = now.year();
    if (selYear < nowYear) {
      return 1;
    } else if (selYear > nowYear) {
      return 0;
    } else {
      // 当前年份，进度 = 当前日年内序号 / 全年天数
      const startOfYear = now.startOf('year');
      const endOfYear = now.endOf('year');
      const daysPassed = now.diff(startOfYear, 'day') + 1;
      const daysInYear = endOfYear.diff(startOfYear, 'day') + 1;
      return Math.min(1, Math.max(0, daysPassed / daysInYear));
    }
  });

  /** 折线图选择器 **/
  const trendType = ref<'total' | 'ton' | 'metal'>('total');
  // 折线图监听: trendType change triggers update already in @change and watch list.

  /** 单位选择器 **/
  const selectedUnit = ref<'all' | 'ckc' | 'ds' | 'sx' | 'jw'>('all');

  /** 业务逻辑约束：单位和金属成本互斥 **/
  // 当选择金属成本时，单位选项（除全部外）应被禁用
  const isUnitDisabled = computed(() => trendType.value === 'metal');
  // 当选择具体单位时，金属成本选项应被禁用
  const isMetalDisabled = computed(() => selectedUnit.value !== 'all');

  // 柱状图模式： false -> 差异模式（默认）, true -> 双柱模式
  const BAR_MODE_KEY = 'cwCostBarMode';
  const barMode = ref<boolean>(localStorage.getItem(BAR_MODE_KEY) === 'double');
  watch(barMode, (val) => {
    localStorage.setItem(BAR_MODE_KEY, val ? 'double' : 'diff');
  });

  /** 监听查询条件自动刷新 **/
  watch([dimension, costDate, selectedUnit], () => fetchCostData());
  watch([barDimension, barDate], () => fetchBarData());
  watch(trendType, () => updateTrendChart());

  /** 业务逻辑约束监听器 **/
  // 当选择金属成本时，自动将单位设置为"全部"
  watch(trendType, (newType) => {
    if (newType === 'metal' && selectedUnit.value !== 'all') {
      selectedUnit.value = 'all';
      message.info('选择金属成本时，单位已自动切换为"全部"');
    }
  });

  // 当选择具体单位时，如果当前是金属成本，自动切换为总成本
  watch(selectedUnit, (newUnit) => {
    if (newUnit !== 'all' && trendType.value === 'metal') {
      trendType.value = 'total';
      message.info('选择具体单位时，成本类型已自动切换为"总成本"');
    }
  });

  onMounted(() => {
    fetchCostData();
    fetchBarData();
  });

  /** 查询成本概览 & 趋势 **/
  async function fetchCostData() {
    try {
      costDataLoading.value = true;
      const dateStr = costDate.value.format('YYYY-MM-DD');
      costStat.value = await costStatistics({ date: dateStr, dimension: dimension.value });
      costTrendList.value = await costTrendByUnit({
        date: dateStr,
        unit: selectedUnit.value,
        trendType: trendType.value
      });
      updateTrendChart();
    } catch (e) {
      console.error(e);
      message.error('成本数据获取失败');
    } finally {
      costDataLoading.value = false;
    }
  }

  /** 更新趋势图表 **/
  function updateTrendChart() {
    if (!costTrendList.value?.length) return;
    const categories = costTrendList.value.map((d) => d.period);

    let actualSeriesName = '';
    let planSeriesName = '';
    let actualDataSeries: number[] = [];
    let planDataSeries: number[] = [];
    let showPlanSeries = false;

    if (trendType.value === 'total') {
      actualSeriesName = '实际总成本';
      planSeriesName = '计划总成本';
      actualDataSeries = costTrendList.value.map((d) => d.totalCost ?? 0);
      planDataSeries = costTrendList.value.map((d) => d.planTotalCost ?? 0);
      showPlanSeries = true; // 总成本显示计划曲线
    } else if (trendType.value === 'ton') {
      actualSeriesName = '实际吨矿成本';
      actualDataSeries = costTrendList.value.map((d) => d.tonCost ?? 0);
      showPlanSeries = false; // 吨矿成本不显示计划曲线
    } else {
      actualSeriesName = '实际金属成本';
      actualDataSeries = costTrendList.value.map((d) => d.metalCost ?? 0);
      showPlanSeries = false; // 金属成本不显示计划曲线
    }

    const series = [
      {
        name: actualSeriesName,
        type: 'line',
        smooth: true,
        data: actualDataSeries,
        lineStyle: { width: 2 },
        itemStyle: { color: '#1890ff' }
      }
    ];

    const legendData = [actualSeriesName];

    // 只有总成本才显示计划曲线
    if (showPlanSeries) {
      series.push({
        name: planSeriesName,
        type: 'line',
        smooth: true,
        data: planDataSeries,
        lineStyle: { width: 2 },
        itemStyle: { color: '#52c41a' }
      });
      legendData.push(planSeriesName);
    }

    setTrendOptions({
      tooltip: {
        trigger: 'axis',
        formatter: function(params: any) {
          let result = params[0].name + '<br/>';
          params.forEach((param: any) => {
            result += param.marker + param.seriesName + ': ' + param.value + '<br/>';
          });
          return result;
        }
      },
      legend: { data: legendData },
      xAxis: { type: 'category', data: categories },
      yAxis: { type: 'value' },
      series: series as any,
    } as any);
  }

  /** 查询单位成本柱状图数据 **/
  async function fetchBarData() {
    try {
      barDataLoading.value = true;
      const dateStr = barDate.value.format('YYYY-MM-DD');
      barList.value = await costExpandBar({ date: dateStr, dimension: barDimension.value });
      updateBarChart();
    } catch (e) {
      console.error(e);
      message.error('单位成本数据获取失败');
    } finally {
      barDataLoading.value = false;
    }
  }

  /** 更新柱状图 **/
  function updateBarChart() {
    if (!barList.value?.length) return;
    const units = barList.value.map((d) => d.unit);
    if (!barMode.value) {
      /* 差异模式（原有实现） */
    // 总成本数据
    const planTotals = barList.value.map((d) => d.planTotalCost ?? 0);
    const diffTotals = barList.value.map((d) => {
      const actual = d.actualTotalCost ?? 0;
      const plan = d.planTotalCost ?? 0;
      return Number((actual - plan).toFixed(4));
    });

    // 吨矿成本数据
    const planTons = barList.value.map((d) => d.planTonCost ?? 0);
    const diffTons = barList.value.map((d) => {
      const actual = d.actualTonCost ?? 0;
      const plan = d.planTonCost ?? 0;
      return Number((actual - plan).toFixed(4));
    });

    // 图表公共项
    const baseOption = {
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' },
        formatter: (params: any) => {
          /* 自定义tooltip：显示计划、实际、差异 */
          const p = params as any[];
          if (!p?.length) return '';
          const plan = p.find((i) => i.seriesName.includes('计划'));
          const diff = p.find((i) => i.seriesName === '差异');
          const actualVal = (plan?.value ?? 0) + (diff?.value ?? 0);
          return `单位：${p[0].name}<br/>计划：${plan?.value}<br/>实际：${actualVal}<br/>差异：${diff?.value}<br/>日计划进度：${(plan?.value ?? 0) * progressRatio.value}`;
        },
      },
      grid: { left: 80, right: 40, bottom: 40, top: 30 },
      xAxis: { type: 'value' },
      yAxis: { type: 'category', data: units },
      legend: { data: ['计划', '差异'] },
    };

    // 总成本图
      setBarTotalOptions({} as any); // placeholder to satisfy type
    // 日计划进度（总成本）散点位置：x=计划*进度, y=单位
    const expectedTotals = planTotals.map((v, idx) => [Number((v * progressRatio.value).toFixed(4)), units[idx]]);

    setBarTotalOptions({
      ...baseOption,
      series: [
        {
          name: '计划',
          type: 'bar',
          stack: 'totalCost',
          label: { show: true, formatter: '{c}' },
          itemStyle: { color: '#91caff' },
          data: planTotals,
          markLine: {
            symbol: 'none',
            label: { show: false },
            lineStyle: { color: '#faad14', width: 2 },
            data: expectedTotals.map((it) => ({ xAxis: it[0] })),
          },
        },
        {
          name: '差异',
          type: 'bar',
          stack: 'totalCost',
          label: { show: true, formatter: (v: any) => (v.value >= 0 ? '+' + v.value : v.value) },
          itemStyle: {
            color: (param: any) => (param.value >= 0 ? '#ff7875' : '#95de64'),
          },
          data: diffTotals,
        },
      ],
      xAxis: {
        type: 'value',
        min: Math.min(...diffTotals) < 0 ? Math.min(...diffTotals) : 0,
      },
      } as any);

    // 吨矿成本图
      setBarTonOptions({} as any);
    // 日计划进度（吨矿）散点位置：x=计划*进度, y=单位
    const expectedTons = planTons.map((v, idx) => [Number((v * progressRatio.value).toFixed(4)), units[idx]]);

    setBarTonOptions({
      ...baseOption,
      yAxis: { type: 'category', data: units },
      series: [
        {
          name: '计划',
          type: 'bar',
          stack: 'tonCost',
          label: { show: true, formatter: '{c}' },
          itemStyle: { color: '#ffd591' },
          data: planTons,
          markLine: {
            symbol: 'none',
            label: { show: false },
            lineStyle: { color: '#faad14', width: 2 },
            data: expectedTons.map((it) => ({ xAxis: it[0] })),
          },
        },
        {
          name: '差异',
          type: 'bar',
          stack: 'tonCost',
          label: { show: true, formatter: (v: any) => (v.value >= 0 ? '+' + v.value : v.value) },
          itemStyle: {
            color: (param: any) => (param.value >= 0 ? '#ff4d4f' : '#73d13d'),
          },
          data: diffTons,
        },
      ],
      xAxis: {
        type: 'value',
        min: Math.min(...diffTons) < 0 ? Math.min(...diffTons) : 0,
      },
      } as any);
      return;
    }
    /* 双柱模式：每个单位两个值 计划(plan) & 实际(actual) */
    const planTotals = barList.value.map((d) => d.planTotalCost ?? 0);
    const actualTotals = barList.value.map((d) => d.actualTotalCost ?? 0);

    const planTons = barList.value.map((d) => d.planTonCost ?? 0);
    const actualTons = barList.value.map((d) => d.actualTonCost ?? 0);

    const commonOption = {
      tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
      legend: { data: ['剩余计划', '实际', '日进度计划'] },
      grid: { left: 80, right: 40, bottom: 40, top: 30 },
      xAxis: { type: 'category', data: units },
      yAxis: { type: 'value' },
    };

    // 总成本图
    setBarTotalOptions({} as any);
    // 将计划拆分为堆叠：日进度计划 + 计划(剩余)
    const progressPlanTotals = planTotals.map((v) => Number((v * progressRatio.value).toFixed(4)));
    const remainPlanTotals = planTotals.map((v, i) => Number((v - progressPlanTotals[i]).toFixed(4)));
    setBarTotalOptions({
      ...commonOption,
      series: [
        {
          name: '日进度计划',
          type: 'bar',
          stack: 'planTotal',
          itemStyle: { color: '#faad14' },
          data: progressPlanTotals,
        },
        {
          name: '剩余计划',
          type: 'bar',
          stack: 'planTotal',
          itemStyle: { color: '#91caff' },
          data: remainPlanTotals,
        },
        { name: '实际', type: 'bar', data: actualTotals, itemStyle: { color: '#ff7875' } },
      ],
    } as any);

    // 吨矿成本图
    setBarTonOptions({} as any);
    // 将计划(吨矿)拆分为堆叠：日进度计划 + 计划(剩余)
    const progressPlanTons = planTons.map((v) => Number((v * progressRatio.value).toFixed(4)));
    const remainPlanTons = planTons.map((v, i) => Number((v - progressPlanTons[i]).toFixed(4)));
    setBarTonOptions({
      ...commonOption,
      series: [
        {
          name: '日进度计划',
          type: 'bar',
          stack: 'planTon',
          itemStyle: { color: '#faad14' },
          data: progressPlanTons,
        },
        {
          name: '剩余计划',
          type: 'bar',
          stack: 'planTon',
          itemStyle: { color: '#91caff' },
          data: remainPlanTons,
        },
        { name: '实际', type: 'bar', data: actualTons, itemStyle: { color: '#ff4d4f' } },
      ],
    } as any);
  }
</script>

<style scoped>
  .cw-cost-page {
    padding: 16px;
  }

  .summary-container {
    display: flex;
    justify-content: space-between;
    flex-wrap: nowrap; /* 保持一行展示 */
    margin-top: 8px;
  }

  .summary-item {
    flex: 1 1 19%; /* 每个卡片约占 1/5 宽度 */
    min-width: 120px;
    background: #f6ffed;
    border: 1px solid #b7eb8f;
    border-radius: 6px;
    text-align: center;
    padding: 12px 8px;
    margin: 4px;
  }

  .summary-item .label {
    color: #389e0d;
    margin-bottom: 4px;
  }

  .summary-item .value {
    font-size: 20px;
    font-weight: 600;
  }

  .unit {
    font-size: 12px;
    margin-left: 2px;
  }

  .period {
    color: #8c8c8c;
    margin-top: 8px;
  }
</style>
